#!/usr/bin/env node

// Load environment variables from .env file
import dotenv from 'dotenv';
dotenv.config();

// Import child_process to spawn the serve command
import { spawn } from 'child_process';

// Get the port from environment variable, default to 3000 if not set
const port = process.env.VITE_PORT || '3000';

// Spawn the serve command with the port
const serveProcess = spawn('serve', ['dist', '-p', port], {
    stdio: 'inherit',
    shell: true,
});

// Handle process exit
serveProcess.on('close', (code) => {
    process.exit(code);
});

// Handle errors
serveProcess.on('error', (err) => {
    console.error('Failed to start serve:', err);
    process.exit(1);
});
